import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class LayerNorm(nn.Module):
    """
    Layer Normalization 层归一化（PyTorch版本）
    
    作用：对每个样本的特征维度进行归一化，使得每层的输入分布更稳定
    公式：LN(x) = γ * (x - μ) / σ + β
    """
    
    def __init__(self, d_model, eps=1e-6):
        """
        初始化LayerNorm
        
        Args:
            d_model: 模型维度（特征维度）
            eps: 防止除零的小常数
        """
        super(LayerNorm, self).__init__()
        self.d_model = d_model
        self.eps = eps
        
        # 可学习参数：使用nn.Parameter让PyTorch知道这些需要梯度更新
        self.gamma = nn.Parameter(torch.ones(d_model))   # 缩放因子γ
        self.beta = nn.Parameter(torch.zeros(d_model))   # 偏移因子β
    
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入张量，形状为 (batch_size, seq_len, d_model)
        
        Returns:
            归一化后的张量
        """
        # 计算最后一个维度（特征维度）的均值和方差
        mean = x.mean(dim=-1, keepdim=True)  # (batch_size, seq_len, 1)
        var = x.var(dim=-1, keepdim=True, unbiased=False)  # (batch_size, seq_len, 1)
        
        # 标准化
        x_norm = (x - mean) / torch.sqrt(var + self.eps)
        
        # 应用可学习参数（广播机制自动处理维度）
        return self.gamma * x_norm + self.beta


def example_layernorm(): 
    #如果你把 example_layernorm() 放在了类定义内部（比如缩进多了），
    # 就会报 NameError: name 'LayerNorm' is not defined，因为类还没定义完，不能在类体内直接调用外部函数。
    
    # 创建测试数据
    torch.manual_seed(42)
    batch_size, seq_len, d_model = 2, 3, 4   # 张量：2个样本，每个样本有4个特征
    x = torch.randn(batch_size, seq_len, d_model) * 5  # 乘以5让差异更明显
    
    print(f"输入形状: {x.shape}")
    print(f"原始输入:\n{x}")
    print()

   
    # 应用LayerNorm
    layer_norm = LayerNorm(d_model)  # 这是在实例化
    x_norm = layer_norm(x)
    
    print(f"LayerNorm输出:\n{x_norm}")

if __name__ == "__main__":
    example_layernorm()